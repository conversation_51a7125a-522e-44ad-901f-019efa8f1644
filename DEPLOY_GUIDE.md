# Guide de Déploiement sur Render

## Problèmes Identifiés et Solutions

### 1. Rate Limiting Discord
**Problème** : Le bot est rate limité par Discord lors de la connexion.

**Solutions appliquées** :
- Augmentation du délai de base de 30 à 60 secondes
- Fermeture propre des sessions avant reconnexion
- Timeouts augmentés (heartbeat: 90s, guild_ready: 15s)

### 2. Credentials Google Drive
**Problème** : Variable `GOOGLE_CREDENTIALS_JSON` vide ou mal formatée.

**Solutions** :
- Vérification que la variable n'est pas vide avant traitement
- Gestion d'erreur améliorée
- Le bot fonctionne sans Google Drive si non configuré

### 3. Sessions aiohttp non fermées
**Problème** : Sessions client non fermées correctement.

**Solutions** :
- Fermeture explicite du bot avant reconnexion
- Attente de 5 secondes pour la fermeture complète

## Instructions de Déploiement

### 1. Configuration des Variables d'Environnement sur Render

Dans le dashboard Render, configurez ces variables :

**Obligatoires** :
- `DISCORD_TOKEN` : Votre token de bot Discord
- `OPENAI_API_KEY` : Votre clé API OpenAI

**Optionnelles** :
- `DISCORD_GUILD_ID` : ID de votre serveur Discord
- `OPENAI_MODEL` : Modèle OpenAI (défaut: gpt-4)
- `OPENAI_EMBED_MODEL` : Modèle d'embedding (défaut: text-embedding-ada-002)
- `GOOGLE_CREDENTIALS_JSON` : JSON complet des credentials Google Drive
- `DRIVE_FILE_ID` : ID du fichier d'index sur Google Drive
- `DRIVE_FOLDER_ID` : ID du dossier Google Drive

### 2. Configuration du Service

**Type de service** : Web Service
**Build Command** : `pip install -r requirements.txt`
**Start Command** : `python start.py`
**Plan** : Free (ou supérieur selon vos besoins)

### 3. Fichiers Modifiés

- `main.py` : Corrections pour rate limiting et gestion des sessions
- `start.py` : Script de démarrage avec vérifications
- `render.yaml` : Configuration Render (optionnel)
- `render-env-guide.md` : Guide des variables d'environnement

### 4. Que Faire Maintenant

1. **Commitez et poussez** les changements vers votre repository
2. **Redéployez** sur Render
3. **Vérifiez les logs** pour confirmer que les erreurs sont résolues
4. **Testez** les commandes `/setup` et `/lore` une fois connecté

### 5. Surveillance

Le bot va maintenant :
- Attendre plus longtemps entre les tentatives de connexion
- Fermer proprement les sessions avant reconnexion
- Continuer à fonctionner même sans Google Drive
- Afficher des logs plus clairs sur l'état de la connexion

### 6. Si le Rate Limiting Persiste

Si vous voyez encore du rate limiting :
- Attendez quelques heures avant de redéployer
- Vérifiez que vous n'avez pas d'autres instances du bot qui tournent
- Contactez le support Discord si le problème persiste
