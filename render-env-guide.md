# Configuration des Variables d'Environnement sur Render

## Variables Obligatoires

### Discord
- `DISCORD_TOKEN` : Token de votre bot Discord
- `DISCORD_GUILD_ID` : ID de votre serveur Discord (optionnel, pour les commandes spécifiques au serveur)

### OpenAI
- `OPENAI_API_KEY` : Clé API OpenAI
- `OPENAI_MODEL` : Modèle à utiliser (par défaut: gpt-4)
- `OPENAI_EMBED_MODEL` : Modèle d'embedding (par défaut: text-embedding-ada-002)

### Google Drive (Optionnel - pour la sauvegarde cloud)
- `GOOGLE_CREDENTIALS_JSON` : Contenu du fichier JSON des credentials Google Drive
- `DRIVE_FILE_ID` : ID du fichier d'index sur Google Drive (optionnel)
- `DRIVE_FOLDER_ID` : ID du dossier Google Drive (optionnel)

### Render
- `PORT` : Port pour le healthcheck HTTP (automatiquement défini par Render)

## Instructions pour Google Drive

1. **Si vous n'avez pas besoin de Google Drive**, laissez ces variables vides. Le bot fonctionnera en mode local uniquement.

2. **Pour configurer Google Drive** :
   - Créez un projet Google Cloud
   - Activez l'API Google Drive
   - Créez un compte de service
   - Téléchargez le fichier JSON des credentials
   - Copiez tout le contenu du fichier JSON dans la variable `GOOGLE_CREDENTIALS_JSON`

## Problèmes Courants

### Rate Limiting Discord
- Le bot gère automatiquement le rate limiting avec des délais exponentiels
- Les délais peuvent aller jusqu'à plusieurs minutes

### Credentials Google Drive
- Assurez-vous que `GOOGLE_CREDENTIALS_JSON` contient un JSON valide
- Si la variable est vide, laissez-la complètement vide (ne mettez pas d'espaces)

### Connexion Discord
- Le bot essaiera de se reconnecter automatiquement jusqu'à 5 fois
- Les timeouts ont été augmentés pour les connexions instables
