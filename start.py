#!/usr/bin/env python3
"""
Script de démarrage optimisé pour Render
Gère les variables d'environnement et le démarrage du bot
"""

import os
import sys
import logging
import asyncio
from main import main

# Configuration du logging pour Render
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    force=True,
    stream=sys.stdout
)

logger = logging.getLogger(__name__)

def check_environment():
    """Vérifie que les variables d'environnement essentielles sont définies"""
    required_vars = ['DISCORD_TOKEN', 'OPENAI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"Variables d'environnement manquantes: {', '.join(missing_vars)}")
        return False
    
    # Vérifier les variables optionnelles
    optional_vars = {
        'DISCORD_GUILD_ID': 'ID du serveur Discord (optionnel)',
        'GOOGLE_CREDENTIALS_JSON': 'Credentials Google Drive (optionnel)',
        'OPENAI_MODEL': 'Modèle OpenAI (par défaut: gpt-4)',
        'OPENAI_EMBED_MODEL': 'Modèle d\'embedding (par défaut: text-embedding-ada-002)'
    }
    
    for var, description in optional_vars.items():
        value = os.getenv(var)
        if value:
            logger.info(f"{var}: configuré")
        else:
            logger.info(f"{var}: non configuré - {description}")
    
    return True

def main_wrapper():
    """Wrapper principal avec gestion d'erreurs"""
    try:
        logger.info("=== Démarrage du Bot Discord Lore RP ===")
        
        # Vérifier l'environnement
        if not check_environment():
            logger.error("Configuration incomplète. Arrêt du programme.")
            sys.exit(1)
        
        # Démarrer le bot
        asyncio.run(main())
        
    except KeyboardInterrupt:
        logger.info("Arrêt demandé par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Erreur fatale: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main_wrapper()
